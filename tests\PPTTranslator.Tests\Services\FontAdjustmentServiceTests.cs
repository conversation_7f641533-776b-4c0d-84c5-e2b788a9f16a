using Microsoft.Extensions.Logging;
using Moq;
using PPTTranslator.Core.Models;
using PPTTranslator.Core.Services;
using Xunit;

namespace PPTTranslator.Tests.Services;

/// <summary>
/// 字体调整服务测试
/// </summary>
public class FontAdjustmentServiceTests
{
    private readonly Mock<ILogger<FontAdjustmentService>> _mockLogger;
    private readonly FontAdjustmentService _fontAdjustmentService;

    public FontAdjustmentServiceTests()
    {
        _mockLogger = new Mock<ILogger<FontAdjustmentService>>();
        _fontAdjustmentService = new FontAdjustmentService(_mockLogger.Object);
    }

    [Theory]
    [InlineData("短文本", "This is a longer translated text", 12.0)]
    [InlineData("这是一个比较长的中文文本内容", "Short", 12.0)]
    [InlineData("相同长度", "Same length", 12.0)]
    public void CalculateAdjustedFontSize_ShouldAdjustBasedOnTextLength(
        string originalText, string translatedText, double originalFontSize)
    {
        // Act
        var adjustedSize = _fontAdjustmentService.CalculateAdjustedFontSize(
            originalText, translatedText, originalFontSize);

        // Assert
        Assert.True(adjustedSize > 0);
        Assert.True(adjustedSize >= originalFontSize * 0.7); // 不应该缩小超过30%
        Assert.True(adjustedSize <= originalFontSize * 1.2); // 不应该放大超过20%
    }

    [Fact]
    public void CalculateAdjustedFontSize_WithEmptyText_ShouldReturnOriginalSize()
    {
        // Arrange
        var originalFontSize = 12.0;

        // Act
        var adjustedSize = _fontAdjustmentService.CalculateAdjustedFontSize(
            "", "", originalFontSize);

        // Assert
        Assert.Equal(originalFontSize, adjustedSize);
    }

    [Fact]
    public void EstimateTextSize_ShouldReturnReasonableEstimate()
    {
        // Arrange
        var text = "这是一个测试文本";
        var fontSize = 12.0;
        var fontName = "Arial";

        // Act
        var textSize = _fontAdjustmentService.EstimateTextSize(text, fontSize, fontName);

        // Assert
        Assert.True(textSize.Width > 0);
        Assert.True(textSize.Height > 0);
        Assert.Equal(text.Length, textSize.CharacterCount);
        Assert.Equal(1, textSize.LineCount);
    }

    [Fact]
    public void EstimateTextSize_WithMultilineText_ShouldCountLines()
    {
        // Arrange
        var text = "第一行\n第二行\n第三行";
        var fontSize = 12.0;
        var fontName = "Arial";

        // Act
        var textSize = _fontAdjustmentService.EstimateTextSize(text, fontSize, fontName);

        // Assert
        Assert.Equal(3, textSize.LineCount);
        Assert.True(textSize.Height > fontSize * 3); // 应该考虑行高
    }

    [Theory]
    [InlineData("zh-CN", "en")]
    [InlineData("en", "zh-CN")]
    [InlineData("ja", "en")]
    [InlineData("ko", "en")]
    public void GetAdjustmentStrategy_ShouldReturnAppropriateStrategy(
        string sourceLanguage, string targetLanguage)
    {
        // Act
        var strategy = _fontAdjustmentService.GetAdjustmentStrategy(sourceLanguage, targetLanguage);

        // Assert
        Assert.NotNull(strategy);
        Assert.True(strategy.MinFontSize > 0);
        Assert.True(strategy.MaxFontSize > strategy.MinFontSize);
        Assert.True(strategy.MaxScaleDown > 0 && strategy.MaxScaleDown <= 1);
        Assert.True(strategy.MaxScaleUp >= 1);
    }

    [Fact]
    public void CalculateBatchAdjustments_ShouldProcessAllElements()
    {
        // Arrange
        var textElements = new List<PPTTextElement>
        {
            new() { Id = "1", Text = "短文本", TranslatedText = "This is a longer text", OriginalFontSize = 12.0, FontName = "Arial" },
            new() { Id = "2", Text = "这是一个较长的文本", TranslatedText = "Short", OriginalFontSize = 14.0, FontName = "Arial" },
            new() { Id = "3", Text = "正常文本", TranslatedText = "Normal text", OriginalFontSize = 16.0, FontName = "Arial" }
        };

        // Act
        var results = _fontAdjustmentService.CalculateBatchAdjustments(textElements);

        // Assert
        Assert.Equal(3, results.Count);
        Assert.All(results, result =>
        {
            Assert.True(result.AdjustedFontSize > 0);
            Assert.True(result.Confidence >= 0 && result.Confidence <= 1);
            Assert.NotEmpty(result.AdjustmentReason);
        });
    }

    [Fact]
    public void ValidateAdjustment_WithValidAdjustment_ShouldReturnValid()
    {
        // Arrange
        var adjustment = new FontAdjustmentResult
        {
            OriginalFontSize = 12.0,
            AdjustedFontSize = 10.0,
            AdjustmentRatio = 10.0 / 12.0,
            Confidence = 0.8
        };

        // Act
        var validation = _fontAdjustmentService.ValidateAdjustment(adjustment);

        // Assert
        Assert.True(validation.IsValid);
        Assert.True(validation.ReadabilityScore >= 5);
    }

    [Fact]
    public void ValidateAdjustment_WithExtremeAdjustment_ShouldReturnWarnings()
    {
        // Arrange
        var adjustment = new FontAdjustmentResult
        {
            OriginalFontSize = 12.0,
            AdjustedFontSize = 4.0, // 极小字体
            AdjustmentRatio = 4.0 / 12.0,
            Confidence = 0.3 // 低置信度
        };

        // Act
        var validation = _fontAdjustmentService.ValidateAdjustment(adjustment);

        // Assert
        Assert.NotEmpty(validation.Warnings);
        Assert.True(validation.ReadabilityScore < 10);
    }

    [Theory]
    [InlineData("Hello World", 12.0, "Arial")]
    [InlineData("你好世界", 12.0, "SimSun")]
    [InlineData("", 12.0, "Arial")]
    public void EstimateTextSize_WithDifferentInputs_ShouldHandleGracefully(
        string text, double fontSize, string fontName)
    {
        // Act
        var textSize = _fontAdjustmentService.EstimateTextSize(text, fontSize, fontName);

        // Assert
        Assert.NotNull(textSize);
        Assert.True(textSize.Width >= 0);
        Assert.True(textSize.Height >= 0);
        Assert.Equal(text.Length, textSize.CharacterCount);
    }
}
