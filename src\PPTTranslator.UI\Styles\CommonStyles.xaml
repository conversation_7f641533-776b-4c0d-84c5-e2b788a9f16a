<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <!-- 卡片样式 -->
    <Style x:Key="CardStyle" TargetType="Border">
        <Setter Property="Background" Value="White"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="8"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 标题样式 -->
    <Style x:Key="HeaderTextStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="{StaticResource HeaderFontSize}"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Margin" Value="{StaticResource DefaultMargin}"/>
    </Style>

    <!-- 子标题样式 -->
    <Style x:Key="SubHeaderTextStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="{StaticResource SubHeaderFontSize}"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
        <Setter Property="Margin" Value="{StaticResource DefaultMargin}"/>
    </Style>

    <!-- 正文样式 -->
    <Style x:Key="BodyTextStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="{StaticResource BodyFontSize}"/>
        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="Margin" Value="{StaticResource SmallMargin}"/>
    </Style>

    <!-- 说明文字样式 -->
    <Style x:Key="CaptionTextStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="{StaticResource CaptionFontSize}"/>
        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBodyLight}"/>
        <Setter Property="FontStyle" Value="Italic"/>
        <Setter Property="Margin" Value="{StaticResource SmallMargin}"/>
    </Style>

    <!-- 主要按钮样式 -->
    <Style x:Key="PrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
        <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="Margin" Value="{StaticResource SmallMargin}"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
    </Style>

    <!-- 次要按钮样式 -->
    <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignOutlinedButton}">
        <Setter Property="BorderBrush" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="Margin" Value="{StaticResource SmallMargin}"/>
    </Style>

    <!-- 成功按钮样式 -->
    <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
        <Setter Property="Background" Value="{StaticResource SuccessBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource SuccessBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="Margin" Value="{StaticResource SmallMargin}"/>
    </Style>

    <!-- 警告按钮样式 -->
    <Style x:Key="WarningButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
        <Setter Property="Background" Value="{StaticResource WarningBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource WarningBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="Margin" Value="{StaticResource SmallMargin}"/>
    </Style>

    <!-- 错误按钮样式 -->
    <Style x:Key="ErrorButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
        <Setter Property="Background" Value="{StaticResource ErrorBrush}"/>
        <Setter Property="BorderBrush" Value="{StaticResource ErrorBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="Margin" Value="{StaticResource SmallMargin}"/>
    </Style>

    <!-- 输入框样式 -->
    <Style x:Key="InputTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
        <Setter Property="Margin" Value="{StaticResource SmallMargin}"/>
        <Setter Property="Padding" Value="8"/>
        <Setter Property="FontSize" Value="{StaticResource BodyFontSize}"/>
    </Style>

    <!-- 下拉框样式 -->
    <Style x:Key="InputComboBoxStyle" TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignOutlinedComboBox}">
        <Setter Property="Margin" Value="{StaticResource SmallMargin}"/>
        <Setter Property="Padding" Value="8"/>
        <Setter Property="FontSize" Value="{StaticResource BodyFontSize}"/>
    </Style>

    <!-- 进度条样式 -->
    <Style x:Key="CustomProgressBarStyle" TargetType="ProgressBar" BasedOn="{StaticResource MaterialDesignLinearProgressBar}">
        <Setter Property="Height" Value="8"/>
        <Setter Property="Margin" Value="{StaticResource SmallMargin}"/>
        <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
    </Style>

    <!-- 分组框样式 -->
    <Style x:Key="GroupBoxStyle" TargetType="GroupBox">
        <Setter Property="Margin" Value="{StaticResource DefaultMargin}"/>
        <Setter Property="Padding" Value="{StaticResource DefaultMargin}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource MaterialDesignDivider}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="GroupBox">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        <Border Grid.Row="0" 
                                Background="{DynamicResource MaterialDesignPaper}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4,4,0,0">
                            <ContentPresenter ContentSource="Header" 
                                            Margin="8,4"
                                            TextBlock.FontWeight="SemiBold"
                                            TextBlock.Foreground="{StaticResource PrimaryBrush}"/>
                        </Border>
                        <Border Grid.Row="1"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="0,0,4,4"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter/>
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 数据网格样式 -->
    <Style x:Key="CustomDataGridStyle" TargetType="DataGrid" BasedOn="{StaticResource MaterialDesignDataGrid}">
        <Setter Property="AutoGenerateColumns" Value="False"/>
        <Setter Property="CanUserAddRows" Value="False"/>
        <Setter Property="CanUserDeleteRows" Value="False"/>
        <Setter Property="IsReadOnly" Value="True"/>
        <Setter Property="SelectionMode" Value="Single"/>
        <Setter Property="GridLinesVisibility" Value="Horizontal"/>
        <Setter Property="HeadersVisibility" Value="Column"/>
        <Setter Property="Margin" Value="{StaticResource SmallMargin}"/>
    </Style>

    <!-- 状态指示器样式 -->
    <Style x:Key="StatusIndicatorStyle" TargetType="Ellipse">
        <Setter Property="Width" Value="12"/>
        <Setter Property="Height" Value="12"/>
        <Setter Property="Margin" Value="4"/>
    </Style>

    <!-- 成功状态 -->
    <Style x:Key="SuccessStatusStyle" TargetType="Ellipse" BasedOn="{StaticResource StatusIndicatorStyle}">
        <Setter Property="Fill" Value="{StaticResource SuccessBrush}"/>
    </Style>

    <!-- 警告状态 -->
    <Style x:Key="WarningStatusStyle" TargetType="Ellipse" BasedOn="{StaticResource StatusIndicatorStyle}">
        <Setter Property="Fill" Value="{StaticResource WarningBrush}"/>
    </Style>

    <!-- 错误状态 -->
    <Style x:Key="ErrorStatusStyle" TargetType="Ellipse" BasedOn="{StaticResource StatusIndicatorStyle}">
        <Setter Property="Fill" Value="{StaticResource ErrorBrush}"/>
    </Style>

</ResourceDictionary>
