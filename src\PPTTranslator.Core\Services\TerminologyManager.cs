using System.Text.Json;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;
using PPTTranslator.Core.Interfaces;
using PPTTranslator.Core.Models;

namespace PPTTranslator.Core.Services;

/// <summary>
/// 术语库管理服务
/// </summary>
public class TerminologyManager : ITerminologyManager
{
    private readonly ILogger<TerminologyManager> _logger;
    private TerminologyDatabase _currentDatabase;

    public TerminologyManager(ILogger<TerminologyManager> logger)
    {
        _logger = logger;
        _currentDatabase = new TerminologyDatabase
        {
            Name = "默认术语库",
            SourceLanguage = "中文",
            TargetLanguage = "英文"
        };
    }

    public TerminologyDatabase CurrentDatabase => _currentDatabase;

    public async Task<TerminologyDatabase> LoadTerminologyAsync(string filePath)
    {
        try
        {
            if (!File.Exists(filePath))
            {
                _logger.LogWarning("术语库文件不存在: {FilePath}", filePath);
                return new TerminologyDatabase();
            }

            var jsonContent = await File.ReadAllTextAsync(filePath);
            var database = JsonSerializer.Deserialize<TerminologyDatabase>(jsonContent);
            
            if (database != null)
            {
                _currentDatabase = database;
                _logger.LogInformation("成功加载术语库: {Name}, 条目数: {Count}", 
                    database.Name, database.Entries.Count);
            }

            return database ?? new TerminologyDatabase();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载术语库失败: {FilePath}", filePath);
            throw;
        }
    }

    public async Task SaveTerminologyAsync(TerminologyDatabase database, string filePath)
    {
        try
        {
            var directory = Path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            database.UpdatedAt = DateTime.Now;
            var jsonContent = JsonSerializer.Serialize(database, new JsonSerializerOptions
            {
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            });

            await File.WriteAllTextAsync(filePath, jsonContent);
            _logger.LogInformation("术语库保存成功: {FilePath}", filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存术语库失败: {FilePath}", filePath);
            throw;
        }
    }

    public void AddTerminologyEntry(TerminologyEntry entry)
    {
        if (string.IsNullOrWhiteSpace(entry.SourceTerm) || string.IsNullOrWhiteSpace(entry.TargetTerm))
        {
            throw new ArgumentException("源术语和目标术语不能为空");
        }

        // 检查是否已存在相同的源术语
        var existingEntry = _currentDatabase.Entries.FirstOrDefault(e => 
            e.SourceTerm.Equals(entry.SourceTerm, StringComparison.OrdinalIgnoreCase));

        if (existingEntry != null)
        {
            // 更新现有条目
            existingEntry.TargetTerm = entry.TargetTerm;
            existingEntry.Category = entry.Category;
            existingEntry.Description = entry.Description;
            existingEntry.Priority = entry.Priority;
            existingEntry.IsEnabled = entry.IsEnabled;
            existingEntry.UpdatedAt = DateTime.Now;
        }
        else
        {
            // 添加新条目
            entry.CreatedAt = DateTime.Now;
            entry.UpdatedAt = DateTime.Now;
            _currentDatabase.Entries.Add(entry);
        }

        _currentDatabase.UpdatedAt = DateTime.Now;
        _logger.LogInformation("添加术语条目: {SourceTerm} -> {TargetTerm}", entry.SourceTerm, entry.TargetTerm);
    }

    public bool RemoveTerminologyEntry(string sourceTerm)
    {
        var entry = _currentDatabase.Entries.FirstOrDefault(e => 
            e.SourceTerm.Equals(sourceTerm, StringComparison.OrdinalIgnoreCase));

        if (entry != null)
        {
            _currentDatabase.Entries.Remove(entry);
            _currentDatabase.UpdatedAt = DateTime.Now;
            _logger.LogInformation("删除术语条目: {SourceTerm}", sourceTerm);
            return true;
        }

        return false;
    }

    public bool UpdateTerminologyEntry(TerminologyEntry entry)
    {
        var existingEntry = _currentDatabase.Entries.FirstOrDefault(e => 
            e.SourceTerm.Equals(entry.SourceTerm, StringComparison.OrdinalIgnoreCase));

        if (existingEntry != null)
        {
            existingEntry.TargetTerm = entry.TargetTerm;
            existingEntry.Category = entry.Category;
            existingEntry.Description = entry.Description;
            existingEntry.Priority = entry.Priority;
            existingEntry.IsEnabled = entry.IsEnabled;
            existingEntry.UpdatedAt = DateTime.Now;
            _currentDatabase.UpdatedAt = DateTime.Now;
            
            _logger.LogInformation("更新术语条目: {SourceTerm}", entry.SourceTerm);
            return true;
        }

        return false;
    }

    public IEnumerable<TerminologyEntry> GetAllEntries()
    {
        return _currentDatabase.Entries.Where(e => e.IsEnabled).OrderByDescending(e => e.Priority);
    }

    public IEnumerable<TerminologyEntry> SearchEntries(string keyword)
    {
        if (string.IsNullOrWhiteSpace(keyword))
        {
            return GetAllEntries();
        }

        return _currentDatabase.Entries.Where(e => 
            e.IsEnabled && 
            (e.SourceTerm.Contains(keyword, StringComparison.OrdinalIgnoreCase) ||
             e.TargetTerm.Contains(keyword, StringComparison.OrdinalIgnoreCase) ||
             e.Category.Contains(keyword, StringComparison.OrdinalIgnoreCase) ||
             e.Description.Contains(keyword, StringComparison.OrdinalIgnoreCase)))
            .OrderByDescending(e => e.Priority);
    }

    public TerminologyPreprocessResult PreprocessText(string text, string sourceLanguage, string targetLanguage)
    {
        var result = new TerminologyPreprocessResult
        {
            OriginalText = text,
            ProcessedText = text
        };

        if (string.IsNullOrWhiteSpace(text))
        {
            return result;
        }

        // 获取启用的术语条目，按长度降序排序（优先匹配长术语）
        var enabledEntries = _currentDatabase.Entries
            .Where(e => e.IsEnabled)
            .OrderByDescending(e => e.SourceTerm.Length)
            .ThenByDescending(e => e.Priority)
            .ToList();

        var processedText = text;
        var placeholderCounter = 0;

        foreach (var entry in enabledEntries)
        {
            // 使用正则表达式进行精确匹配（避免部分匹配）
            var pattern = $@"\b{Regex.Escape(entry.SourceTerm)}\b";
            var matches = Regex.Matches(processedText, pattern, RegexOptions.IgnoreCase);

            foreach (Match match in matches)
            {
                var placeholder = $"__TERM_{placeholderCounter++}__";
                
                var terminologyMatch = new TerminologyMatch
                {
                    Entry = entry,
                    StartIndex = match.Index,
                    Length = match.Length,
                    MatchedText = match.Value,
                    ReplacementText = entry.TargetTerm
                };

                result.Matches.Add(terminologyMatch);
                result.PlaceholderMap[placeholder] = entry.TargetTerm;

                // 替换为占位符
                processedText = processedText.Remove(match.Index, match.Length)
                    .Insert(match.Index, placeholder);
            }
        }

        result.ProcessedText = processedText;
        
        _logger.LogDebug("术语预处理完成，匹配到 {Count} 个术语", result.Matches.Count);
        return result;
    }

    public string PostprocessText(string translatedText, TerminologyPreprocessResult preprocessResult)
    {
        if (string.IsNullOrWhiteSpace(translatedText) || !preprocessResult.PlaceholderMap.Any())
        {
            return translatedText;
        }

        var result = translatedText;

        // 将占位符替换为目标术语
        foreach (var kvp in preprocessResult.PlaceholderMap)
        {
            result = result.Replace(kvp.Key, kvp.Value);
        }

        _logger.LogDebug("术语后处理完成，替换了 {Count} 个占位符", preprocessResult.PlaceholderMap.Count);
        return result;
    }

    public void ClearTerminology()
    {
        _currentDatabase.Entries.Clear();
        _currentDatabase.UpdatedAt = DateTime.Now;
        _logger.LogInformation("术语库已清空");
    }

    public async Task ImportTerminologyAsync(string filePath, string format)
    {
        // 实现不同格式的导入逻辑
        switch (format.ToLowerInvariant())
        {
            case "json":
                await ImportFromJsonAsync(filePath);
                break;
            case "csv":
                await ImportFromCsvAsync(filePath);
                break;
            case "xlsx":
                await ImportFromExcelAsync(filePath);
                break;
            default:
                throw new NotSupportedException($"不支持的文件格式: {format}");
        }
    }

    public async Task ExportTerminologyAsync(string filePath, string format)
    {
        // 实现不同格式的导出逻辑
        switch (format.ToLowerInvariant())
        {
            case "json":
                await ExportToJsonAsync(filePath);
                break;
            case "csv":
                await ExportToCsvAsync(filePath);
                break;
            case "xlsx":
                await ExportToExcelAsync(filePath);
                break;
            default:
                throw new NotSupportedException($"不支持的导出格式: {format}");
        }
    }

    private async Task ImportFromJsonAsync(string filePath)
    {
        var database = await LoadTerminologyAsync(filePath);
        foreach (var entry in database.Entries)
        {
            AddTerminologyEntry(entry);
        }
    }

    private async Task ImportFromCsvAsync(string filePath)
    {
        // CSV导入实现
        var lines = await File.ReadAllLinesAsync(filePath);
        for (int i = 1; i < lines.Length; i++) // 跳过标题行
        {
            var parts = lines[i].Split(',');
            if (parts.Length >= 2)
            {
                var entry = new TerminologyEntry
                {
                    SourceTerm = parts[0].Trim('"'),
                    TargetTerm = parts[1].Trim('"'),
                    Category = parts.Length > 2 ? parts[2].Trim('"') : "",
                    Description = parts.Length > 3 ? parts[3].Trim('"') : ""
                };
                AddTerminologyEntry(entry);
            }
        }
    }

    private async Task ImportFromExcelAsync(string filePath)
    {
        // Excel导入实现（需要引用Excel处理库）
        throw new NotImplementedException("Excel导入功能待实现");
    }

    private async Task ExportToJsonAsync(string filePath)
    {
        await SaveTerminologyAsync(_currentDatabase, filePath);
    }

    private async Task ExportToCsvAsync(string filePath)
    {
        var lines = new List<string>
        {
            "源术语,目标术语,类别,描述"
        };

        foreach (var entry in _currentDatabase.Entries)
        {
            lines.Add($"\"{entry.SourceTerm}\",\"{entry.TargetTerm}\",\"{entry.Category}\",\"{entry.Description}\"");
        }

        await File.WriteAllLinesAsync(filePath, lines);
    }

    private async Task ExportToExcelAsync(string filePath)
    {
        // Excel导出实现（需要引用Excel处理库）
        throw new NotImplementedException("Excel导出功能待实现");
    }
}
