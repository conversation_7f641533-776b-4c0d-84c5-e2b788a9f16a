using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using PPTTranslator.API.Interfaces;

namespace PPTTranslator.API.Clients;

/// <summary>
/// Ollama本地模型客户端
/// </summary>
public class OllamaClient : ITranslationClient
{
    private readonly HttpClient _httpClient;
    private readonly TranslationClientConfig _config;
    private readonly ILogger<OllamaClient> _logger;

    public OllamaClient(HttpClient httpClient, TranslationClientConfig config, ILogger<OllamaClient> logger)
    {
        _httpClient = httpClient;
        _config = config;
        _logger = logger;

        _httpClient.Timeout = TimeSpan.FromSeconds(_config.TimeoutSeconds);
    }

    public async Task<string> TranslateAsync(string text, string sourceLanguage, string targetLanguage, string model)
    {
        try
        {
            var prompt = BuildTranslationPrompt(text, sourceLanguage, targetLanguage);
            var requestBody = new
            {
                model = model,
                prompt = prompt,
                stream = false,
                options = new
                {
                    temperature = 0.3,
                    top_p = 0.9,
                    max_tokens = 2000
                }
            };

            var jsonContent = JsonSerializer.Serialize(requestBody);
            var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

            var generateUrl = $"{_config.ServerUrl.TrimEnd('/')}/api/generate";
            var response = await _httpClient.PostAsync(generateUrl, content);
            response.EnsureSuccessStatusCode();

            var responseContent = await response.Content.ReadAsStringAsync();
            var responseData = JsonSerializer.Deserialize<OllamaResponse>(responseContent);

            if (!string.IsNullOrEmpty(responseData?.Response))
            {
                var translatedText = responseData.Response.Trim();
                _logger.LogDebug("Ollama翻译成功: {OriginalLength} -> {TranslatedLength} 字符", 
                    text.Length, translatedText.Length);
                return translatedText;
            }

            throw new Exception("Ollama API返回结果为空");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Ollama翻译失败: {Text}", text);
            throw;
        }
    }

    public async Task<string> DetectLanguageAsync(string text)
    {
        try
        {
            var prompt = $"Detect the language of the following text and return only the language code (e.g., zh-CN, en, ja):\n{text}";
            var requestBody = new
            {
                model = _config.DefaultModel,
                prompt = prompt,
                stream = false,
                options = new
                {
                    temperature = 0.1,
                    max_tokens = 10
                }
            };

            var jsonContent = JsonSerializer.Serialize(requestBody);
            var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

            var generateUrl = $"{_config.ServerUrl.TrimEnd('/')}/api/generate";
            var response = await _httpClient.PostAsync(generateUrl, content);
            response.EnsureSuccessStatusCode();

            var responseContent = await response.Content.ReadAsStringAsync();
            var responseData = JsonSerializer.Deserialize<OllamaResponse>(responseContent);

            if (!string.IsNullOrEmpty(responseData?.Response))
            {
                return responseData.Response.Trim();
            }

            return "unknown";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Ollama语言检测失败: {Text}", text);
            return "unknown";
        }
    }

    public async Task<bool> ValidateConnectionAsync()
    {
        try
        {
            var tagsUrl = $"{_config.ServerUrl.TrimEnd('/')}/api/tags";
            var response = await _httpClient.GetAsync(tagsUrl);
            response.EnsureSuccessStatusCode();

            _logger.LogInformation("Ollama连接验证成功");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Ollama连接验证失败");
            return false;
        }
    }

    public async Task<List<string>> GetAvailableModelsAsync()
    {
        try
        {
            var tagsUrl = $"{_config.ServerUrl.TrimEnd('/')}/api/tags";
            var response = await _httpClient.GetAsync(tagsUrl);
            response.EnsureSuccessStatusCode();

            var responseContent = await response.Content.ReadAsStringAsync();
            var responseData = JsonSerializer.Deserialize<OllamaTagsResponse>(responseContent);

            if (responseData?.Models != null)
            {
                return responseData.Models.Select(m => m.Name).ToList();
            }

            return new List<string>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取Ollama模型列表失败");
            return new List<string>();
        }
    }

    private string BuildTranslationPrompt(string text, string sourceLanguage, string targetLanguage)
    {
        var sourceLanguageName = GetLanguageName(sourceLanguage);
        var targetLanguageName = GetLanguageName(targetLanguage);

        return $@"Translate the following {sourceLanguageName} text to {targetLanguageName}. Requirements:
1. Maintain the original tone and style
2. Ensure accurate and natural translation
3. Keep technical terms consistent
4. Return only the translation result without any explanation

Original text:
{text}

Translation:";
    }

    private string GetLanguageName(string languageCode)
    {
        return languageCode.ToLowerInvariant() switch
        {
            "zh-cn" or "zh" or "chinese" => "Chinese",
            "en" or "english" => "English",
            "ja" or "japanese" => "Japanese",
            "ko" or "korean" => "Korean",
            "fr" or "french" => "French",
            "de" or "german" => "German",
            "es" or "spanish" => "Spanish",
            "ru" or "russian" => "Russian",
            _ => languageCode
        };
    }
}

/// <summary>
/// Ollama API响应模型
/// </summary>
public class OllamaResponse
{
    public string Model { get; set; } = string.Empty;
    public string Response { get; set; } = string.Empty;
    public bool Done { get; set; }
    public string CreatedAt { get; set; } = string.Empty;
}

/// <summary>
/// Ollama模型列表响应
/// </summary>
public class OllamaTagsResponse
{
    public OllamaModel[]? Models { get; set; }
}

public class OllamaModel
{
    public string Name { get; set; } = string.Empty;
    public string ModifiedAt { get; set; } = string.Empty;
    public long Size { get; set; }
    public string Digest { get; set; } = string.Empty;
}
