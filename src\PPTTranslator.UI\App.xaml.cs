using System.Windows;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using PPTTranslator.API.Clients;
using PPTTranslator.API.Interfaces;
using PPTTranslator.Core.Interfaces;
using PPTTranslator.Core.Services;
using PPTTranslator.UI.ViewModels;
using PPTTranslator.UI.Views;

namespace PPTTranslator.UI;

/// <summary>
/// App.xaml 的交互逻辑
/// </summary>
public partial class App : Application
{
    private IHost? _host;

    protected override void OnStartup(StartupEventArgs e)
    {
        // 创建主机
        _host = Host.CreateDefaultBuilder()
            .ConfigureServices(ConfigureServices)
            .ConfigureLogging(logging =>
            {
                logging.ClearProviders();
                logging.AddConsole();
                logging.AddDebug();
            })
            .Build();

        // 启动主机
        _host.Start();

        // 显示主窗口
        var mainWindow = _host.Services.GetRequiredService<MainWindow>();
        mainWindow.Show();

        base.OnStartup(e);
    }

    protected override void OnExit(ExitEventArgs e)
    {
        _host?.Dispose();
        base.OnExit(e);
    }

    private void ConfigureServices(IServiceCollection services)
    {
        // 配置服务
        services.AddSingleton<ConfigurationService>();

        // 核心服务
        services.AddSingleton<ITerminologyManager, TerminologyManager>();
        services.AddSingleton<ITextPreprocessor, TextPreprocessor>();
        services.AddSingleton<IFontAdjustmentService, FontAdjustmentService>();
        services.AddSingleton<IPPTProcessor, PPTProcessor>();
        services.AddSingleton<ITranslationService, TranslationService>();

        // HTTP客户端
        services.AddHttpClient<ZhipuAIClient>();
        services.AddHttpClient<OllamaClient>();

        // 翻译客户端（根据配置选择）
        services.AddSingleton<ITranslationClient>(provider =>
        {
            var config = provider.GetRequiredService<ConfigurationService>().Config;
            var httpClientFactory = provider.GetRequiredService<IHttpClientFactory>();
            var logger = provider.GetRequiredService<ILogger<ZhipuAIClient>>();

            var clientConfig = new TranslationClientConfig
            {
                ApiKey = config.Translation.ApiKey,
                ServerUrl = config.Translation.ServerUrl,
                TimeoutSeconds = config.Translation.TimeoutSeconds,
                RetryCount = config.Translation.RetryCount,
                DefaultModel = config.Translation.DefaultModel
            };

            return config.Translation.Provider.ToLowerInvariant() switch
            {
                "zhipuai" => new ZhipuAIClient(httpClientFactory.CreateClient(), clientConfig, logger),
                "ollama" => new OllamaClient(httpClientFactory.CreateClient(), clientConfig, 
                    provider.GetRequiredService<ILogger<OllamaClient>>()),
                _ => new ZhipuAIClient(httpClientFactory.CreateClient(), clientConfig, logger)
            };
        });

        // ViewModels
        services.AddTransient<MainViewModel>();
        services.AddTransient<TranslationViewModel>();
        services.AddTransient<TerminologyViewModel>();
        services.AddTransient<SettingsViewModel>();

        // Views
        services.AddTransient<MainWindow>();
    }
}
