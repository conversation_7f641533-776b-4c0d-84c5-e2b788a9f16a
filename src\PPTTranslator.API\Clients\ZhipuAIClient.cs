using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using PPTTranslator.API.Interfaces;

namespace PPTTranslator.API.Clients;

/// <summary>
/// 智谱AI翻译客户端
/// </summary>
public class ZhipuAIClient : ITranslationClient
{
    private readonly HttpClient _httpClient;
    private readonly TranslationClientConfig _config;
    private readonly ILogger<ZhipuAIClient> _logger;

    private const string BaseUrl = "https://open.bigmodel.cn/api/paas/v4/chat/completions";

    public ZhipuAIClient(HttpClient httpClient, TranslationClientConfig config, ILogger<ZhipuAIClient> logger)
    {
        _httpClient = httpClient;
        _config = config;
        _logger = logger;

        // 设置HTTP客户端
        _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_config.ApiKey}");
        _httpClient.Timeout = TimeSpan.FromSeconds(_config.TimeoutSeconds);
    }

    public async Task<string> TranslateAsync(string text, string sourceLanguage, string targetLanguage, string model)
    {
        try
        {
            var prompt = BuildTranslationPrompt(text, sourceLanguage, targetLanguage);
            var requestBody = new
            {
                model = model,
                messages = new[]
                {
                    new { role = "system", content = "你是一个专业的翻译助手，请准确翻译用户提供的文本。" },
                    new { role = "user", content = prompt }
                },
                temperature = 0.3,
                max_tokens = 2000
            };

            var jsonContent = JsonSerializer.Serialize(requestBody);
            var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync(BaseUrl, content);
            response.EnsureSuccessStatusCode();

            var responseContent = await response.Content.ReadAsStringAsync();
            var responseData = JsonSerializer.Deserialize<ZhipuAIResponse>(responseContent);

            if (responseData?.Choices?.Length > 0)
            {
                var translatedText = responseData.Choices[0].Message.Content;
                _logger.LogDebug("翻译成功: {OriginalLength} -> {TranslatedLength} 字符", 
                    text.Length, translatedText.Length);
                return translatedText;
            }

            throw new Exception("API返回结果为空");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "智谱AI翻译失败: {Text}", text);
            throw;
        }
    }

    public async Task<string> DetectLanguageAsync(string text)
    {
        try
        {
            var prompt = $"请检测以下文本的语言，只返回语言代码（如：zh-CN, en, ja等）：\n{text}";
            var requestBody = new
            {
                model = _config.DefaultModel,
                messages = new[]
                {
                    new { role = "user", content = prompt }
                },
                temperature = 0.1,
                max_tokens = 10
            };

            var jsonContent = JsonSerializer.Serialize(requestBody);
            var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync(BaseUrl, content);
            response.EnsureSuccessStatusCode();

            var responseContent = await response.Content.ReadAsStringAsync();
            var responseData = JsonSerializer.Deserialize<ZhipuAIResponse>(responseContent);

            if (responseData?.Choices?.Length > 0)
            {
                return responseData.Choices[0].Message.Content.Trim();
            }

            return "unknown";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "语言检测失败: {Text}", text);
            return "unknown";
        }
    }

    public async Task<bool> ValidateConnectionAsync()
    {
        try
        {
            var testText = "Hello";
            await TranslateAsync(testText, "en", "zh-CN", _config.DefaultModel);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "连接验证失败");
            return false;
        }
    }

    public async Task<List<string>> GetAvailableModelsAsync()
    {
        // 智谱AI支持的模型列表
        return new List<string>
        {
            "glm-4-flash",
            "glm-4",
            "glm-4-air",
            "glm-4-airx"
        };
    }

    private string BuildTranslationPrompt(string text, string sourceLanguage, string targetLanguage)
    {
        var sourceLanguageName = GetLanguageName(sourceLanguage);
        var targetLanguageName = GetLanguageName(targetLanguage);

        return $@"请将以下{sourceLanguageName}文本翻译成{targetLanguageName}，要求：
1. 保持原文的语气和风格
2. 确保翻译准确、自然
3. 保持专业术语的一致性
4. 只返回翻译结果，不要添加任何解释

原文：
{text}";
    }

    private string GetLanguageName(string languageCode)
    {
        return languageCode.ToLowerInvariant() switch
        {
            "zh-cn" or "zh" or "chinese" => "中文",
            "en" or "english" => "英文",
            "ja" or "japanese" => "日文",
            "ko" or "korean" => "韩文",
            "fr" or "french" => "法文",
            "de" or "german" => "德文",
            "es" or "spanish" => "西班牙文",
            "ru" or "russian" => "俄文",
            _ => languageCode
        };
    }
}

/// <summary>
/// 智谱AI API响应模型
/// </summary>
public class ZhipuAIResponse
{
    public ZhipuAIChoice[]? Choices { get; set; }
    public ZhipuAIUsage? Usage { get; set; }
}

public class ZhipuAIChoice
{
    public ZhipuAIMessage Message { get; set; } = new();
    public string FinishReason { get; set; } = string.Empty;
}

public class ZhipuAIMessage
{
    public string Role { get; set; } = string.Empty;
    public string Content { get; set; } = string.Empty;
}

public class ZhipuAIUsage
{
    public int PromptTokens { get; set; }
    public int CompletionTokens { get; set; }
    public int TotalTokens { get; set; }
}
